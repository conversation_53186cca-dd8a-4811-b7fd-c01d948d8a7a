#include <stdio.h>
#include <limits.h> 
#define ARRAY_SIZE 200

int memory[ARRAY_SIZE] = {INT_MAX};



void* NewMalloc(int dataType){
    if (dataType<=0) return NULL;

    int max_free_mem = 0;
    int free_index = 0;
    for (int i=0; i < ARRAY_SIZE-1; i++){
        if (memory[i]== INT_MAX){
            int free_mem=0;
            int _index = i;
            while (memory[i] == INT_MAX){
                free_mem++;
                i++;
            }
            if (free_mem > max_free_mem) {
                max_free_mem=free_mem;
                free_index=_index;
            }
        }
    }
    if (max_free_mem >= dataType+1){
        memory[free_index] = dataType;
        return &memory[free_index+1];
    }
    return NULL;

}

void NewFree(void *ptr){
    if (!ptr) return;
    int index = (int*)ptr - &memory[0];
    int size = memory[index-1];
    for (int i = 0; i < size; i++){
        memory[index-1+i] = INT_MAX;
    }

}

int main(void){
    init_memory();

    int *a = (int*)NewMalloc(3);  // allocate 3 blocks
    if (a) {
        a[0] = 42;
        a[1] = -7;
        a[2] = 100;
    }

    NewFree(a, 3); // you must pass size to free

    return 0;
}