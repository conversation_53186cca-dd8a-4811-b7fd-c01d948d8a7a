#include <stdio.h>
#include <math.h>

#define MAX_HISTORY 100


int is_int(double num);
void print_menu();
double add(double a, double b);
double subtract(double a, double b);
double divide(double a, double b);
double multiply(double a, double b) ;
int gcd(int a, int b) ;
double power(double a, int b);
int lcm(int a, int b) ;
double to_rad(double a);
double to_deg(double a);
double sin_(double s);
double cos_(double s);
double tan_(double s);
double inverse(double s);
int remainder_(int num, int divisor);
int is_odd(int num);
double percentage(double num1, double num2);
void print_history();

// for function pointers
double power_ad(double a, double b){ return power(a, (int)b); }
double lcm_ad(double a, double b){ return lcm((int)a, (int)b); }
double gcd_ad(double a, double b){ return gcd((int)a, (int)b); }
double sin_ad(double a, double _b){ return sin_(a); }
double cos_ad(double a, double _b){ return cos_(a); }
double tan_ad(double a, double _b){ return tan_(a); }
double inv_ad(double a, double _b){ return inverse(a); }
double rem_ad(double a, double b){ return remainder_((int)a, (int)b); }
double odd_ad(double a, double _b){ return (double) is_odd((int)a); }
double history_ad(double _a, double _b){ 
  print_history();
  return 0.0;
}


double (*ptrs[16])(double,double) = {
  add,
  subtract,
  multiply,
  divide,
  power_ad,
  lcm_ad,
  gcd_ad,
  sin_ad,
  cos_ad,
  tan_ad,
  inv_ad,
  rem_ad,
  odd_ad,
  percentage,
  NULL, // exit
  history_ad
};


typedef enum {
    ADD = '+',
    SUBTRACT = '-',
    MULTIPLY = '*',
    DIVIDE = '/',
    POWER = '^',
    LCM = 'l',
    GCD = 'g',
    SIN = 's',
    COS = 'c',
    TAN = 't',
    INVERSE = 'i',
    REMAINDER = 'r',
    ODD_EVEN = 'o',
    PERCENTAGE = 'p'
} op_type;

typedef struct {
    op_type op;
    double num1;
    double num2;
    double ans;
} operation;

operation history[MAX_HISTORY];
int curr_operation = 0;

int main() {
    while (1)
    {
        curr_operation = curr_operation%MAX_HISTORY;
        int choice;
        double ans=0;
        print_menu();
        printf("\nEnter your choice: ");
        if (scanf("%d", &choice) == 0 || !(choice <= 16 && choice >= 1)){
            printf("Invalid Choice\n");
            continue;
        }
        double num1=0,num2=0;
        if ((choice >=1 && choice <=7) || choice == 12 || choice == 14){
            printf("Enter Number 1: ");
            if (scanf("%lf", &num1) == 0){
                printf("Invalid number\n");
                continue;
            }
            printf("Enter Number 2: ");
            if (scanf("%lf", &num2) == 0){
                printf("Invalid number\n");
                continue;
            }
        } else if (choice != 15 && choice != 16){
            printf((choice==13)?"Enter a number":"Enter an angle: ");
            if (scanf("%lf", &num1) == 0){
                printf("Invalid number\n");
                continue;
            }
        } else if (choice==15){
            return 0;
        }
        
        double (*fn)(double,double) = ptrs[choice-1];
        // History
        if (choice ==16){
          fn(num1,num2);
          curr_operation++;
          continue;
        }

        ans = fn(num1,num2);
        if (choice == 13) {
           printf((ans != 0.0) ? "Odd\n" : "Even\n");
        } else if (choice == 10 && ans == -2) {
           printf("undefined\n");
        } else if (choice == 11 && num1 == 0.0) {
           printf("undefined\n");
        } else {
           printf("Ans = %.5f\n", ans);
        }

        curr_operation++;
    }
}

// helper functions

void print_menu(){
        printf("\n\n############################################\n");
        printf("##   SCS 1301 - Scientific Calculator     ##\n");
        printf("##              by Inuka                  ##\n");
        printf("############################################\n");
        printf("############################################\n");
        printf("#  1. Addition        8. Sine             #\n");
        printf("#  2. Subtraction     9. Cosine           #\n");
        printf("#  3. Multiplication 10. Tangent          #\n");
        printf("#  4. Division       11. Inverse          #\n");
        printf("#  5. Power          12. Remainder        #\n");
        printf("#  6. LCM            13. Odd or Even      #\n");
        printf("#  7. GCD            14. Percentage       #\n");
        printf("#                                         #\n");
        printf("# 15. Exit           16. History          #\n");
        printf("############################################\n");
        printf("############################################\n");

}

double to_rad(double a){
    return M_PI/180.0*a;
}

double to_deg(double a){
    return 180.0/M_PI*a;
}

int is_int(double num){
    return (floor(num)==num);
}

void print_history(){
    printf("\n\n History\n");
    for (int i = curr_operation-1; i >=0; i--){
        if ((history[i].ans)== '\0') return;
        switch (history[i].op){
            case ADD:
                printf("%.4f+%.4f = %.4f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case SUBTRACT:
                printf("%.4f-%.4f = %.4f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case MULTIPLY:
                printf("%.4f*%.4f = %.4f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case DIVIDE:
                printf("%.4f/%.4f = %.4f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case POWER:
                printf("%.4f^%.0f = %.4f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case LCM:
                printf("lcm of %.0f,%.0f = %.0f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case GCD:
                printf("gcd of %.0f,%.0f = %.0f\n",history[i].num1,history[i].num2,history[i].ans);
                break;
            case SIN:
                printf("sin %f = %.3f\n",history[i].num1,history[i].ans);
                break;
            case COS:
                printf("cos %f = %.3f\n",history[i].num1,history[i].ans);
                break;
            case TAN:
                printf("tan %f = %.3f\n",history[i].num1,history[i].ans);
                break;
            case INVERSE:
                printf("inverse %f = %.3f\n",history[i].num1,history[i].ans);
                break;
            case REMAINDER:
                printf("remainder %f = %.0f\n",history[i].num1,history[i].ans);
                break;
            case ODD_EVEN:
                printf("%.0f is %s", history[i].num1,(history[i].ans)? "odd" : "even");
                break;
            case PERCENTAGE:
                printf("percentage %f and %f = %.3f\n",history[i].num1,history[i].num2,history[i].ans);
                break;

        }
    }

}

// main functions

double add(double a, double b){
    double ans = a+b;
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = ADD;
    history[curr_operation].ans = ans;
    return ans;
}

double subtract(double a, double b){
    double ans = a-b;
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = SUBTRACT;
    history[curr_operation].ans = ans;
    return ans;
}

double multiply(double a, double b) {
    double ans = a*b;
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = MULTIPLY;
    history[curr_operation].ans = ans;
    return ans;
}

double divide(double a, double b){

    int c;
    while (1){
        printf("##########################\n");
        printf("##########################\n");
        printf("## 1. Integer Division ##\n");
        printf("## 2. Normal Division ##\n");
        printf("##########################\n");
        printf("##########################\n");
        printf("Choice: ");
        if (scanf("%d", &c)==0 || (c != 1 && c!=2)){
            continue;
        } else {
            break;
        }
    }
    double ans = (c==1) ? (int)a/(int)b : a/b;
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = DIVIDE;
    history[curr_operation].ans = ans;
    return ans;
}

double power(double a, int b){
    int out = 1;
    for (int i=0; i < b; i++){
        out = out*a;
    }
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = POWER;
    history[curr_operation].ans = out;
    return out;
}

int gcd(int a, int b) {
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = GCD;
    while (b != 0) {
        int temp = b;
        b = a % b;
        a = temp;
    }
    history[curr_operation].ans = a;
    return a;
}

int lcm(int a, int b) {
    int ans = (a * b) / gcd(a, b);
    history[curr_operation].num1 = a;
    history[curr_operation].num2 = b;
    history[curr_operation].op = LCM;
    history[curr_operation].ans = ans;
    return ans;
}

double sin_(double s){
    double ans = sin(to_rad(s));
    history[curr_operation].num1 = s;
    history[curr_operation].num2 = 0;
    history[curr_operation].op = SIN;
    history[curr_operation].ans = ans;
    return ans;
}

double cos_(double s){
    double ans = cos(to_rad(s));
    history[curr_operation].num1 = s;
    history[curr_operation].num2 = 0;
    history[curr_operation].op = COS;
    history[curr_operation].ans = ans;
    return ans;
}

double tan_(double s){
    double ans = tan(to_rad(s));
    if ((int) s%90 == 0){
        ans = -2;
    }
    history[curr_operation].num1 = s;
    history[curr_operation].num2 = 0;
    history[curr_operation].op = TAN;
    history[curr_operation].ans = ans;
    return ans;
}

double inverse(double s){
    double ans = 1.0/s;
    if (s == 0){
        return -1;
    }
    history[curr_operation].num1 = s;
    history[curr_operation].num2 = 0;
    history[curr_operation].op = INVERSE;
    history[curr_operation].ans = ans;
    return ans;
}

int remainder_(int num, int divisor){
    int ans = num%divisor;
    history[curr_operation].num1 = num;
    history[curr_operation].num2 = divisor;
    history[curr_operation].op = REMAINDER;
    history[curr_operation].ans = ans;
    return ans;
}

int is_odd(int num){
    int ans = num%2;
    history[curr_operation].num1 = num;
    history[curr_operation].num2 = 0;
    history[curr_operation].op = ODD_EVEN;
    history[curr_operation].ans = ans;
    return ans;
}

double percentage(double num1, double num2){
    double ans = (num1/num2)*100;
    history[curr_operation].num1 = num1;
    history[curr_operation].num2 = num2;
    history[curr_operation].op = PERCENTAGE;
    history[curr_operation].ans = ans;
    return ans;
}
